# Offers Management System

## Table of Contents
- [Overview](#overview)
- [Offer Types and Processing](#offer-types-and-processing)
- [User Experience Journeys](#user-experience-journeys)
- [System Architecture](#system-architecture)
- [Data Models and Relationships](#data-models-and-relationships)
- [Business Logic and State Management](#business-logic-and-state-management)
- [Integration Architecture](#integration-architecture)
- [API Endpoints](#api-endpoints)
- [Performance and Security](#performance-and-security)
- [Implementation Guidelines](#implementation-guidelines)

## Overview

The Offers Management System is Perkd's comprehensive promotional framework that enables merchants to create, distribute, and manage digital promotional campaigns across multiple channels. The system serves as a platform supporting diverse merchant needs while providing users with a unified, intuitive experience for discovering and redeeming offers.

### Value Proposition

**For Users:**
- Single app for all promotional offers across participating merchants
- Flexible redemption options (in-store, online, in-app) based on convenience
- Automatic application of eligible offers during checkout
- Social sharing capabilities with friends and family

**For Merchants:**
- Unified campaign creation with multi-channel distribution
- Real-time performance analytics across all redemption channels
- Reduced integration complexity through single platform API
- Advanced targeting and personalization capabilities

**For the Platform:**
- Scalable architecture supporting diverse merchant requirements
- Comprehensive tracking and attribution across channels
- Fraud prevention and security measures
- Performance optimization for high-volume operations

### Core Capabilities

- **Multi-Channel Redemption**: Seamless redemption across in-store, online, and in-app channels
- **Intelligent Offer Matching**: Context-aware offer presentation based on user behavior and location
- **Real-Time State Management**: Live synchronization of offer availability and redemption status
- **Social Distribution**: User-to-user sharing with policy enforcement and tracking
- **Automated Application**: Smart application of eligible offers during checkout processes
- **Comprehensive Analytics**: Full attribution tracking and performance measurement

## Offer Types and Processing

The system supports three distinct offer types, each optimized for different promotional strategies and merchant requirements.

### Processing Decision Matrix

| Offer Type | Processing System | User Interface | Redemption Channels | Auto-Application |
|------------|-------------------|----------------|---------------------|------------------|
| **Discount** | Redemption Engine | Slide/Tap to Redeem | In-Store, Online, In-App | ✅ In-App Checkout |
| **Voucher (Payment)** | Payment System | Payment Method Selection | In-App Only | ✅ Checkout Payment |
| **Ticket** | Redemption Engine | Check-in Interface | In-Store Only | ❌ Manual Check-in |

### 1. Discount Offers

**Purpose**: Standard promotional offers providing percentage or fixed-amount price reductions.

**Characteristics:**
- Processed through the redemption engine with standard authentication methods
- Support all redemption channels (in-store, online, in-app)
- Automatically applied during in-app checkout when eligible
- Can have usage limits, time constraints, and item qualifiers

**User Experience:**
- **Multi-Channel**: User selects preferred redemption method
- **In-App**: Automatic application during checkout
- **In-Store/Online**: Authentication required (QR scan, NFC, or manual code)

**Technical Identification:**
```javascript
kind === "discount"
```

### 2. Payment Vouchers

**Purpose**: Fixed-value payment instruments processed as payment methods during checkout.

**Characteristics:**
- Processed through the payment system, NOT the redemption engine
- Available only for in-app redemption during checkout
- Appear as selectable payment methods alongside credit cards
- Single-use with fixed monetary value

**User Experience:**
- Presented as offers in the user's offer list
- Selected as payment method during checkout (not redeemed as discount offers)
- Automatically deduct their value from order total
- No authentication methods required (payment method selection only)

**Technical Identification:**
```javascript
kind === "voucher" && options?.payment === true && discount?.kind === "fixed"
```

### 3. Tickets

**Purpose**: Time-bound offers for events, services, or experiences with check-in functionality.

**Characteristics:**
- Processed through redemption engine with specialized check-in features
- Optimized for NFC and QR code authentication methods
- Include venue, timing, and location-specific validation

**User Experience:**
- Check-in interface rather than traditional redemption
- Location and time-based validation
- Integration with calendar and reminder systems

**Technical Identification:**
```javascript
kind === "ticket"
```

## User Experience Journeys

This section focuses on what users actually see and experience, rather than technical implementation details.

### Journey 1: Discovering and Redeeming a Coffee Discount

**Scenario**: Sarah receives a 20% off offer from her favorite beauty brand.

1. **Discovery**: Offer appears in Sarah's card view with clear value proposition
2. **Interest**: Sarah taps to view details - images, terms, expiration date
3. **Channel Selection** (only if offer supports multiple channels): 
   - **Multi-Channel Offer**: Two buttons displayed - "In Store" and "In App"
   - **Single-Channel Offer**: Direct redemption button appears (no selection needed)
4. **Redemption Flow**: 
   - **If Sarah selects "In Store"**: Button transforms to slide-to-redeem interface
   - **If Sarah selects "In App"**: Direct navigation to shop widget
   - **Single-Channel**: Appropriate interface loads immediately
5. **Redemption**:
   - **In-Store**: Sarah slides to redeem → Shows barcode / QR code for cashier to scan
   - **In-App**: Navigates to beauty brand's shop widget → Discount automatically applied at checkout
6. **Confirmation**: Order confirmation with discount applied and receipt generated

**Key User Insights:**
- Channel options appear based on context and offer configuration
- Authentication is invisible to users (handled by system)
- Different channels provide appropriate interfaces for the context

### Journey 2: Using a Voucher

**Scenario**: Mark receives a $25 voucher for an online retailer.

1. **Discovery**: Voucher appears in Mark's offer list as "$25 Voucher"
2. **Usage Decision**: Mark decides to shop for a new shirt
3. **Shopping**: Mark browses items and adds $40 shirt to cart
4. **Checkout**: 
   - Mark sees payment options: "$25 Voucher", Apple Pay / Google Pay, Credit Card, etc.
   - "$25 Voucher" auto selected
   - Remaining $15 charged to Apple Pay / Google Pay / Credit Card
5. **Completion**: Order placed with voucher automatically applied

**Key User Insights:**
- Payment vouchers appear as payment methods, not traditional "offers to redeem"
- Integration is seamless within existing checkout flow
- Partial payment handling is automatic

### Journey 3: Checking into a Concert Ticket

**Scenario**: Emma has a digital ticket for a concert at a local venue.

1. **Preparation**: Emma receives reminder notification 2 hours before event
2. **Arrival**: Emma approaches venue entrance
3. **Check-in Options**:
   - **NFC**: Emma taps her phone on venue's NFC reader (falls back to QR code scanning if NFC unavailable)
4. **Validation**: System verifies Emma's location, event timing, and ticket authenticity
5. **Access**: Successful check-in grants venue access and updates ticket status

**Key User Insights:**
- Location and timing are automatically validated
- Multiple authentication methods provide flexibility
- Real-time status updates keep users informed

### Journey 4: Redeeming at a Vending Machine

**Scenario**: David has a "Free Snack" voucher and approaches a smart vending machine at his office.

1. **Machine Detection**: David approaches the vending machine
2. **Product Selection**: David select a snack from the vending machine's display
3. **Offer Selection**: David opens his Perkd app and selects the free snack voucher
4. **Authentication Options**:
   - **NFC**: David taps his phone on the machine's NFC emitter
   - **QR Code**: David taps "Redeem" to open QR code scanner → Scan QR code on machine
5. **Dispensing**: Perkd validates offer and updates offer status → Machine dispenses product

**Key User Insights:**
- Machine integration feels seamless and modern
- Offer and Product eligibility is validated in real-time

### Journey 5: Online Redemption with Deep Link Integration

**Scenario**: Lisa receives a "50% off first ride" offer for Uber and wants to use it for her evening commute.

1. **Offer Discovery**: Lisa sees the Uber discount offer in her Perkd app
2. **Redemption Initiation**: Lisa taps "Redeem" button
3. **Deep Link Generation**: Perkd generates a custom URL with embedded redemption code
4. **Partner App Launch**:
   - **Uber App Installed**: Deep link opens Uber app with promo code auto-populated
   - **No App**: Opens Uber website with code automatically applied to account
5. **Seamless Integration**: Lisa sees "50% off - Perkd Promotion Applied" message in Uber
6. **Ride Booking**: Lisa books her ride with discount automatically calculated
7. **Confirmation**: 
   - Uber processes the ride with discount
   - Offer status updates to "REDEEMED" in Perkd app

**Key User Insights:**
- Zero manual code entry required - completely seamless experience

### Channel Selection Logic

Users see channel options based on offer configuration:

**Single Channel Offers:**
- **In-App Only**: Direct "Auto-applied when shop in app" button
- **In-Store Only**: Direct "Redeem" button
- **Online Only**: Direct "Redeem" button

**Multi-Channel Offers:**
- **Two Buttons Displayed**:
  - "In App" (for in-app redemption)
  - "In Store" or "Online" (for traditional redemption)
- **User Selection**: Buttons transform interface based on selection
- **Context Switching**: Users can change selection before confirming redemption

## System Architecture

The offers system follows a sophisticated multi-layered architecture emphasizing modularity, scalability, and maintainability.

### High-Level Architecture

```mermaid
graph TB
    subgraph "🎯 User Experience Layer"
        UX1[Offer Discovery<br/>Location & Context-based]
        UX2[Offer Details<br/>Rich Media & Terms]
        UX3[Redemption Flow<br/>Multi-channel Support]
        UX4[Sharing Interface<br/>Social Distribution]
    end

    subgraph "📱 Presentation Layer"
        P1[Offer Containers<br/>src/containers/Offer/]
        P2[Offer Components<br/>src/components/Offer/]
        P3[Offer Widgets<br/>Card Integration]
        P4[Navigation<br/>Modal & Stack]
    end

    subgraph "🎮 Business Logic Layer"
        B1[Offer Controller<br/>src/controllers/Offer.js]
        B2[Offer Actions<br/>src/lib/common/actions/offer.js]
        B3[Offer Services<br/>src/lib/common/services/offer.js]
        B4[State Management<br/>src/lib/offers.js]
    end

    subgraph "💾 Data Layer"
        D1[Offer Model<br/>src/lib/models/Offer.js]
        D2[Related Models<br/>Code, Discount, Redemption]
        D3[Realm Database<br/>Local Storage]
        D4[Sync Engine<br/>Bidirectional Sync]
    end

    subgraph "🌐 External Integration"
        E1[Backend APIs<br/>REST Endpoints]
        E2[Card Services<br/>Integration]
        E3[Payment Systems<br/>Transaction Processing]
        E4[Location Services<br/>Geofencing]
    end

    %% User Experience Flow
    UX1 --> UX2
    UX2 --> UX3
    UX2 --> UX4
    UX3 --> UX1

    %% Presentation Layer Connections
    UX1 --> P1
    UX2 --> P1
    UX3 --> P1
    UX4 --> P1
    P1 --> P2
    P1 --> P3
    P1 --> P4

    %% Business Logic Connections
    P1 --> B1
    P2 --> B1
    B1 --> B2
    B1 --> B3
    B2 --> B4
    B3 --> B4

    %% Data Layer Connections
    B2 --> D1
    B3 --> D1
    D1 --> D2
    D1 --> D3
    D3 --> D4

    %% External Integration
    B3 --> E1
    B1 --> E2
    B3 --> E3
    B1 --> E4
    D4 --> E1

    %% Styling with darker backgrounds and white text
    classDef ux fill:#1a202c,stroke:#2d3748,stroke-width:3px,color:#ffffff
    classDef presentation fill:#2d3748,stroke:#4a5568,stroke-width:2px,color:#ffffff
    classDef business fill:#2b6cb0,stroke:#3182ce,stroke-width:2px,color:#ffffff
    classDef data fill:#2f855a,stroke:#38a169,stroke-width:2px,color:#ffffff
    classDef external fill:#805ad5,stroke:#9f7aea,stroke-width:2px,color:#ffffff

    class UX1,UX2,UX3,UX4 ux
    class P1,P2,P3,P4 presentation
    class B1,B2,B3,B4 business
    class D1,D2,D3,D4 data
    class E1,E2,E3,E4 external
```

### Architectural Principles

- **Separation of Concerns**: Clear boundaries between presentation, business logic, and data layers
- **Event-Driven Communication**: Loose coupling through centralized event system
- **State Management**: Centralized offer state management with real-time updates
- **Service Integration**: Modular integration with card, payment, and location services
- **Performance Optimization**: Multi-level caching and lazy loading strategies

### Widget-Based Architecture

The offers system leverages the Perkd widget framework to provide modular, reusable components that integrate seamlessly with the card management system:

```javascript
export default class Offer extends DataWidget {
    constructor(definition, owner, data = {}, credentials) {
        super(definition, owner, data, credentials);
        
        // Automatic filtering to prevent widget conflicts
        this.configureOfferTypes();
    }

    configureOfferTypes() {
        const excludedKinds = this.getExcludedKinds();
        if (!this.param.kinds && excludedKinds.length > 0) {
            const availableKinds = this.getAvailableKinds(excludedKinds);
            this.param.kinds = availableKinds.length ? availableKinds : ['offer'];
        }
    }
}
```

### Channel Processing Architecture

The system routes offers through appropriate processing systems based on type and configuration:

```mermaid
graph LR
    subgraph "Offer Input"
        A[User Redeems Offer]
    end

    subgraph "Processing Decision"
        B{Channel Router}
        B --> C{Is Payment Voucher?}
        B --> D{Authentication Method}
    end

    subgraph "Processing Systems"
        E[Payment System<br/>Voucher Checkout]
        F[Redemption Engine<br/>Standard Flow]
        G[Authentication<br/>QR/NFC/Manual]
    end

    subgraph "Completion"
        H[Payment Confirmation]
        I[Redemption Confirmation]
    end

    A --> B
    C -->|Yes| E
    C -->|No| F
    D --> G
    E --> H
    F --> G
    G --> I
```

## Data Models and Relationships

### Core Offer Schema

The primary `Offer` model supports all offer types with a unified structure:

```javascript
// Offer Model Core Properties
{
    // Identity & Classification
    id: String,                    // Unique identifier
    masterId: String,             // Template reference
    cardId: String,               // Card association
    kind: String,                 // "discount" | "voucher" | "ticket"
    
    // Content & Presentation
    name: String,                 // Display name
    title: String,                // Short title
    description: String,          // Detailed description
    images: [OfferImage],         // Media assets
    
    // Behavior Configuration
    options: {
        payment: Boolean,         // Is payment voucher?
        buttonLink: Object,       // Channel URLs
        authorize: String         // Auth method override
    },
    
    // Timing & Lifecycle
    startTime: Date,              // Activation time
    endTime: Date,                // Expiration time
    state: String,                // Current lifecycle state
    
    // Redemption Configuration
    redemption: {
        channels: [String],       // Supported channels
        authorize: String,        // Required auth method
        limit: Number,           // Usage limit
        remain: Number,          // Remaining uses
        multiple: Boolean        // Multi-use allowed?
    },
    
    // Integration Data
    code: OfferCode,             // Redemption codes
    discount: OfferDiscount,     // Discount configuration
    touchpoints: [TouchPoint]    // Interaction history
}
```

### Offer Type Specializations

Different offer types use the same core schema with specific configurations:

**Discount Offers:**
```javascript
{
    kind: "discount",
    options: { payment: false },
    discount: { kind: "PERCENTAGE", value: 20 },
    redemption: { 
        channels: ["instore", "online", "perkd"],
        authorize: "scan" 
    }
}
```

**Payment Vouchers:**
```javascript
{
    kind: "voucher",
    options: { payment: true },
    discount: { kind: "FIXED", value: 2500 }, // $25.00 in cents
    redemption: { 
        channels: ["perkd"] // Payment system only
    }
}
```

**Event Tickets:**
```javascript
{
    kind: "ticket",
    venue: "Madison Square Garden",
    checkin: { window: 7200000 }, // 2 hours in ms
    redemption: {
        channels: ["instore", "perkd"],
        authorize: "nfc" // Preferred for venues
    }
}
```

### State Management Model

Offers follow a comprehensive state lifecycle:

```mermaid
stateDiagram-v2
    [*] --> PENDING : Created
    PENDING --> UPCOMING : Future Start Time
    PENDING --> ACTIVE : Current Time
    
    UPCOMING --> ACTIVE : Start Time Reached
    UPCOMING --> EXPIRED : End Time Passed
    
    ACTIVE --> REDEEMED : Used
    ACTIVE --> TRANSFERRING : Share Started
    ACTIVE --> EXPIRED : End Time Passed
    
    REDEEMED --> FULLY_REDEEMED : Usage Exhausted
    TRANSFERRING --> TRANSFERRED : Share Complete
    
    EXPIRED --> [*]
    FULLY_REDEEMED --> [*]
    TRANSFERRED --> [*]
```

### Channel Compatibility Matrix

| Offer Type | In-Store | Online | In-App | Payment System |
|------------|----------|--------|--------|----------------|
| **Discount** | ✅ QR/NFC/Manual | ✅ Manual Code | ✅ Auto-Apply | ❌ |
| **Payment Voucher** | ❌ | ❌ | ✅ Payment Selection | ✅ Primary |
| **Redemption Voucher** | ✅ QR/NFC/Manual | ✅ Manual Code | ✅ Auto-Apply | ❌ |
| **Ticket** | ✅ QR/NFC Check-in | ❌ | ✅ Check-in | ❌ |

## Business Logic and State Management

### Offer State Calculation

The system dynamically calculates offer states based on multiple factors:

```javascript
const calculateOfferState = (offer, currentTime = new Date()) => {
    const { redemption, startTime, endTime, state, when } = offer;
    
    // Terminal states take precedence
    if (['TRANSFERRED', 'TRANSFERRING', 'FULLY_REDEEMED', 'CANCELLED'].includes(state)) {
        return state;
    }
    
    // Usage exhaustion check
    if (redemption?.remain !== null && redemption?.remain < 1) {
        return 'FULLY_REDEEMED';
    }
    
    // Redemption status check
    if (when?.redeemed || when?.authorized) {
        return 'REDEEMED';
    }
    
    // Time-based states
    if (startTime && new Date(startTime) > currentTime) {
        return 'UPCOMING';
    }
    
    if (endTime && new Date(endTime) < currentTime) {
        return 'EXPIRED';
    }
    
    return 'ACTIVE';
};
```

### Business Rules Engine

#### Redemption Eligibility

```javascript
const isRedemptionEligible = (offer, context) => {
    // Basic state validation
    if (!['ACTIVE', 'UPCOMING'].includes(calculateOfferState(offer))) {
        return { eligible: false, reason: 'Invalid state' };
    }
    
    // Channel validation
    const requestedChannel = context.channel;
    if (!offer.redemption.channels.includes(requestedChannel)) {
        return { eligible: false, reason: 'Channel not supported' };
    }
    
    // Usage limit validation
    if (offer.redemption.remain !== null && offer.redemption.remain < 1) {
        return { eligible: false, reason: 'Usage limit exceeded' };
    }
    
    // Location validation (for venue-specific offers)
    if (offer.venue && !validateLocation(context.location, offer.venue)) {
        return { eligible: false, reason: 'Location restriction' };
    }
    
    return { eligible: true };
};
```

#### Processing Route Decision

```javascript
const determineProcessingRoute = (offer) => {
    // Payment vouchers go through payment system
    if (offer.kind === 'voucher' && offer.options?.payment === true) {
        return {
            system: 'payment',
            interface: 'payment_method_selection',
            authentication: 'none'
        };
    }
    
    // All other offers go through redemption system
    return {
        system: 'redemption',
        interface: determineRedemptionInterface(offer),
        authentication: offer.redemption.authorize || 'manual'
    };
};

const determineRedemptionInterface = (offer) => {
    const channels = offer.redemption.channels;
    const hasPerkd = channels.includes('perkd');
    const hasTraditional = channels.includes('instore') || channels.includes('online');
    
    if (hasPerkd && hasTraditional) {
        return 'multi_channel_selection';
    } else if (hasPerkd && offer.options?.buttonLink) {
        return 'shop_widget_auto';
    } else if (hasTraditional) {
        return 'slide_to_redeem';
    }
    
    return 'not_redeemable';
};
```

### Advanced Business Logic Patterns

#### Sharing and Transfer Logic

```javascript
const validateSharing = (offer, shareRequest) => {
    // Check if offer supports sharing
    if (!offer.options?.shareable) {
        return { valid: false, reason: 'Sharing not permitted' };
    }
    
    // Validate recipient eligibility
    const recipientEligible = checkRecipientEligibility(
        shareRequest.recipient,
        offer.sharePolicy
    );
    
    if (!recipientEligible) {
        return { valid: false, reason: 'Recipient not eligible' };
    }
    
    // Check sharing limits
    const sharingHistory = getOfferSharingHistory(offer.id);
    if (sharingHistory.length >= offer.sharePolicy.maxShares) {
        return { valid: false, reason: 'Sharing limit exceeded' };
    }
    
    return { valid: true };
};
```

#### Fraud Prevention Rules

```javascript
const validateRedemptionSecurity = (offer, redemptionContext) => {
    const checks = [];
    
    // Velocity check - prevent rapid redemption attempts
    const recentAttempts = getRecentRedemptionAttempts(
        redemptionContext.userId, 
        300000 // 5 minutes
    );
    
    if (recentAttempts.length > 3) {
        checks.push({ 
            type: 'velocity', 
            risk: 'high', 
            action: 'require_additional_auth' 
        });
    }
    
    // Location validation for venue-specific offers
    if (offer.venue && redemptionContext.location) {
        const distance = calculateDistance(
            redemptionContext.location,
            offer.venue.coordinates
        );
        
        if (distance > offer.venue.maxDistance) {
            checks.push({
                type: 'location',
                risk: 'medium',
                action: 'require_manual_review'
            });
        }
    }
    
    // Device correlation check
    const knownDevices = getUserDeviceHistory(redemptionContext.userId);
    if (!knownDevices.includes(redemptionContext.deviceId)) {
        checks.push({
            type: 'device',
            risk: 'low',
            action: 'log_for_analysis'
        });
    }
    
    return {
        riskScore: calculateRiskScore(checks),
        requiredActions: checks.map(c => c.action),
        approved: checks.every(c => c.risk !== 'high')
    };
};
```

## Integration Architecture

### External Service Integration

The offers system integrates with multiple external services while maintaining loose coupling:

```mermaid
graph TB
    subgraph "Offers System"
        OS1[Offer Controller]
        OS2[State Manager] 
        OS3[Channel Router]
        OS4[Event System]
    end
    
    subgraph "Platform Services"
        PS1[Card Management<br/>Association & Lifecycle]
        PS2[Payment Processing<br/>Transaction Handling]
        PS3[User Management<br/>Authentication & Profile]
        PS4[Analytics<br/>Performance Tracking]
    end
    
    subgraph "External Systems"
        ES1[Merchant APIs<br/>Inventory & Pricing]
        ES2[Location Services<br/>GPS & Geofencing]
        ES3[Notification Services<br/>Push & Email]
        ES4[Social Platforms<br/>Sharing Integration]
    end
    
    OS1 --> PS1
    OS1 --> PS2
    OS2 --> PS3
    OS4 --> PS4
    
    OS3 --> ES1
    OS2 --> ES2
    OS4 --> ES3
    OS1 --> ES4
```

### Integration Patterns

#### Service Communication

```javascript
// Event-driven integration for loose coupling
class OfferController {
    async redeemOffer(offerId, context) {
        try {
            // Validate eligibility
            const offer = await this.getOffer(offerId);
            const eligibility = await this.validateEligibility(offer, context);
            
            if (!eligibility.eligible) {
                throw new Error(eligibility.reason);
            }
            
            // Process redemption
            const result = await this.processRedemption(offer, context);
            
            // Emit events for other systems
            this.eventSystem.emit('offer.redeemed', {
                offerId,
                userId: context.userId,
                channel: context.channel,
                result
            });
            
            // Update analytics
            this.analytics.track('offer_redemption_success', {
                offer_type: offer.kind,
                channel: context.channel,
                value: offer.discount?.value
            });
            
            return result;
            
        } catch (error) {
            // Error handling and reporting
            this.eventSystem.emit('offer.redemption_failed', {
                offerId,
                error: error.message,
                context
            });
            
            throw error;
        }
    }
}
```

#### External API Integration

```javascript
// Merchant API integration with fallback handling
class MerchantIntegration {
    async validateOfferInventory(offer, items) {
        const merchantApi = this.getMerchantApi(offer.merchantId);
        
        try {
            // Primary validation through merchant API
            const validation = await merchantApi.validateInventory({
                items,
                offerId: offer.id,
                timestamp: new Date().toISOString()
            });
            
            return validation;
            
        } catch (apiError) {
            // Fallback to cached data
            console.warn('Merchant API unavailable, using cached validation');
            
            return this.getCachedInventoryValidation(offer.merchantId, items);
        }
    }
}
```

## API Endpoints

### Core Offer Operations

#### Offer Retrieval
```http
GET /offers/{id}
Authorization: Bearer {token}

Response:
{
    "id": "offer_123",
    "kind": "discount",
    "name": "20% Off Coffee",
    "state": "ACTIVE",
    "discount": {
        "kind": "PERCENTAGE",
        "value": 20
    },
    "redemption": {
        "channels": ["instore", "perkd"],
        "authorize": "scan",
        "limit": 1,
        "remain": 1
    }
}
```

#### Offer Redemption
```http
POST /offers/{id}/redeem
Authorization: Bearer {token}
Content-Type: application/json

{
    "channel": "instore",
    "location": {
        "latitude": 40.7128,
        "longitude": -74.0060
    },
    "context": {
        "merchantId": "merchant_456",
        "storeId": "store_789"
    }
}

Response:
{
    "success": true,
    "redemptionId": "redemption_abc",
    "newState": "REDEEMED",
    "appliedDiscount": {
        "amount": 500,
        "currency": "USD"
    }
}
```

#### Bulk Offer Retrieval
```http
GET /offers/batch
Authorization: Bearer {token}
Content-Type: application/json

{
    "ids": ["offer_123", "offer_456", "offer_789"],
    "includeExpired": false,
    "channel": "perkd"
}

Response:
{
    "offers": [...],
    "meta": {
        "total": 3,
        "active": 2,
        "expired": 0
    }
}
```

### Error Handling Standards

All API endpoints follow consistent error response formats:

```json
{
    "error": {
        "code": "OFFER_NOT_REDEEMABLE",
        "message": "This offer cannot be redeemed at this time",
        "details": {
            "reason": "expired",
            "expiredAt": "2025-01-15T10:30:00Z"
        },
        "retryable": false
    }
}
```

### Common Error Codes

| Code | Description | Retryable |
|------|-------------|-----------|
| `OFFER_NOT_FOUND` | Offer does not exist | No |
| `OFFER_EXPIRED` | Offer has expired | No |
| `CHANNEL_NOT_SUPPORTED` | Redemption channel not available | No |
| `USAGE_LIMIT_EXCEEDED` | Redemption limit reached | No |
| `LOCATION_RESTRICTED` | User location not eligible | No |
| `INVENTORY_UNAVAILABLE` | Required items out of stock | Yes |
| `MERCHANT_API_ERROR` | External service temporary failure | Yes |
| `RATE_LIMIT_EXCEEDED` | Too many requests | Yes |

## Performance and Security

### Performance Optimization

#### Data Access Patterns

```javascript
// Efficient offer loading with proper caching
class OfferDataManager {
    async getActiveOffers(userId, options = {}) {
        const cacheKey = `offers:active:${userId}`;
        
        // Check memory cache first
        let offers = this.memoryCache.get(cacheKey);
        if (offers && !options.forceRefresh) {
            return offers;
        }
        
        // Load from local database with optimized query
        offers = await this.database.query('Offer')
            .filtered('state IN $0 AND endTime > $1', 
                ['ACTIVE', 'UPCOMING'], 
                new Date()
            )
            .sorted('priority DESC, endTime ASC')
            .slice(0, options.limit || 50);
        
        // Cache results
        this.memoryCache.set(cacheKey, offers, 300000); // 5 minutes
        
        return offers;
    }
}
```

#### UI Performance

```javascript
// Virtualized list implementation for large offer collections
import { VirtualizedList } from 'react-native';

const OfferList = ({ offers }) => {
    const renderOffer = useCallback(({ item, index }) => (
        <OfferCard 
            key={item.id}
            offer={item}
            onPress={() => navigateToOffer(item)}
        />
    ), []);
    
    return (
        <VirtualizedList
            data={offers}
            renderItem={renderOffer}
            keyExtractor={item => item.id}
            getItemCount={data => data.length}
            getItem={(data, index) => data[index]}
            removeClippedSubviews={true}
            maxToRenderPerBatch={10}
            windowSize={10}
        />
    );
};
```

### Security Implementation

#### Authentication and Authorization

```javascript
// Multi-layer security validation
class SecurityValidator {
    async validateRedemption(offer, context) {
        // User authentication check
        if (!this.isUserAuthenticated(context.userId)) {
            throw new SecurityError('User not authenticated');
        }
        
        // Device validation
        const deviceTrusted = await this.validateDevice(context.deviceId);
        if (!deviceTrusted) {
            await this.requireAdditionalAuth(context.userId);
        }
        
        // Offer integrity check
        const offerValid = await this.validateOfferIntegrity(offer);
        if (!offerValid) {
            throw new SecurityError('Offer integrity compromised');
        }
        
        // Rate limiting
        await this.enforceRateLimit(context.userId, 'redemption');
        
        return true;
    }
}
```

#### Data Protection

```javascript
// Encryption for sensitive offer data
class OfferSecurity {
    encryptSensitiveData(offer) {
        const sensitiveFields = ['code', 'barcode', 'redemptionToken'];
        const encrypted = { ...offer };
        
        sensitiveFields.forEach(field => {
            if (encrypted[field]) {
                encrypted[field] = this.encrypt(encrypted[field]);
            }
        });
        
        return encrypted;
    }
    
    decryptForDisplay(encryptedOffer) {
        // Only decrypt when actually needed for display
        return this.decrypt(encryptedOffer);
    }
}
```

## Implementation Guidelines

### Development Best Practices

#### Widget Development Pattern

```javascript
// Standard widget extension pattern
export class CustomOfferWidget extends OfferWidget {
    constructor(definition, owner, data, credentials) {
        super(definition, owner, data, credentials);
        this.customizeForSpecificUseCase();
    }
    